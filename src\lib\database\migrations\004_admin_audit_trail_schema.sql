-- Admin Audit Trail Database Schema Migration
-- Implements comprehensive audit logging for all admin operations
-- Provides compliance and security monitoring capabilities

-- Admin Audit Log Table
-- Tracks all admin operations across the system
CREATE TABLE IF NOT EXISTS admin_audit_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Core audit information
    action VARCHAR(100) NOT NULL, -- e.g., 'create_tool', 'update_category', 'delete_user'
    resource_type VARCHAR(50) NOT NULL, -- e.g., 'tool', 'category', 'user', 'config'
    resource_id VARCHAR(255), -- ID of the affected resource
    resource_name VARCHAR(255), -- Human-readable name of the resource
    
    -- User and session information
    performed_by VA<PERSON><PERSON><PERSON>(255) NOT NULL, -- User identifier who performed the action
    user_role VARCHAR(50), -- Role of the user at time of action
    session_id VARCHAR(255), -- User session identifier
    request_id VARCHAR(255), -- Request tracking ID
    
    -- Request details
    http_method VARCHAR(10), -- GET, POST, PUT, DELETE
    endpoint VARCHAR(255), -- API endpoint called
    ip_address INET, -- User's IP address
    user_agent TEXT, -- User's browser/client info
    
    -- Action details and changes
    action_details JSONB, -- Detailed information about the action
    old_values JSONB, -- Previous values (for updates/deletes)
    new_values JSONB, -- New values (for creates/updates)
    
    -- Status and outcome
    status VARCHAR(20) DEFAULT 'success' CHECK (status IN ('success', 'failed', 'partial')),
    error_message TEXT, -- Error details if action failed
    
    -- Timestamps
    performed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Metadata
    severity VARCHAR(20) DEFAULT 'medium' CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    category VARCHAR(50) DEFAULT 'admin' CHECK (category IN ('admin', 'security', 'data', 'system', 'user')),
    tags TEXT[], -- Searchable tags for categorization
    
    -- Compliance and retention
    retention_period INTERVAL DEFAULT INTERVAL '7 years', -- Data retention period
    is_sensitive BOOLEAN DEFAULT FALSE, -- Contains sensitive information
    compliance_flags TEXT[] -- Compliance-related flags (GDPR, SOX, etc.)
);

-- User Session Tracking Table
-- Tracks admin user sessions for security monitoring
CREATE TABLE IF NOT EXISTS admin_user_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id VARCHAR(255) NOT NULL,
    session_id VARCHAR(255) UNIQUE NOT NULL,
    
    -- Session details
    login_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    logout_time TIMESTAMP WITH TIME ZONE,
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Security information
    ip_address INET NOT NULL,
    user_agent TEXT,
    login_method VARCHAR(50) DEFAULT 'api_key', -- 'api_key', 'jwt', 'oauth'
    
    -- Session status
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'expired', 'terminated', 'suspicious')),
    termination_reason VARCHAR(100), -- Reason for session termination
    
    -- Security flags
    is_suspicious BOOLEAN DEFAULT FALSE,
    failed_attempts INTEGER DEFAULT 0,
    security_alerts TEXT[],
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Audit Log Statistics Table
-- Stores aggregated audit statistics for performance
CREATE TABLE IF NOT EXISTS audit_log_statistics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Time period
    period_start TIMESTAMP WITH TIME ZONE NOT NULL,
    period_end TIMESTAMP WITH TIME ZONE NOT NULL,
    period_type VARCHAR(20) NOT NULL CHECK (period_type IN ('hour', 'day', 'week', 'month')),
    
    -- Statistics
    total_actions INTEGER DEFAULT 0,
    successful_actions INTEGER DEFAULT 0,
    failed_actions INTEGER DEFAULT 0,
    
    -- Action breakdown
    action_breakdown JSONB, -- Count by action type
    resource_breakdown JSONB, -- Count by resource type
    user_breakdown JSONB, -- Count by user
    
    -- Security metrics
    suspicious_activities INTEGER DEFAULT 0,
    security_alerts INTEGER DEFAULT 0,
    failed_logins INTEGER DEFAULT 0,
    
    -- Performance metrics
    avg_response_time DECIMAL(10,3),
    peak_activity_hour INTEGER,
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints
    UNIQUE(period_start, period_end, period_type)
);

-- Indexes for Performance
-- Admin audit log indexes
CREATE INDEX IF NOT EXISTS idx_admin_audit_log_performed_at ON admin_audit_log(performed_at DESC);
CREATE INDEX IF NOT EXISTS idx_admin_audit_log_performed_by ON admin_audit_log(performed_by);
CREATE INDEX IF NOT EXISTS idx_admin_audit_log_action ON admin_audit_log(action);
CREATE INDEX IF NOT EXISTS idx_admin_audit_log_resource_type ON admin_audit_log(resource_type);
CREATE INDEX IF NOT EXISTS idx_admin_audit_log_resource_id ON admin_audit_log(resource_id);
CREATE INDEX IF NOT EXISTS idx_admin_audit_log_status ON admin_audit_log(status);
CREATE INDEX IF NOT EXISTS idx_admin_audit_log_category ON admin_audit_log(category);
CREATE INDEX IF NOT EXISTS idx_admin_audit_log_severity ON admin_audit_log(severity);
CREATE INDEX IF NOT EXISTS idx_admin_audit_log_session_id ON admin_audit_log(session_id);

-- Composite indexes for common queries
CREATE INDEX IF NOT EXISTS idx_admin_audit_log_user_time ON admin_audit_log(performed_by, performed_at DESC);
CREATE INDEX IF NOT EXISTS idx_admin_audit_log_resource_time ON admin_audit_log(resource_type, resource_id, performed_at DESC);
CREATE INDEX IF NOT EXISTS idx_admin_audit_log_action_time ON admin_audit_log(action, performed_at DESC);

-- User sessions indexes
CREATE INDEX IF NOT EXISTS idx_admin_user_sessions_user_id ON admin_user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_admin_user_sessions_session_id ON admin_user_sessions(session_id);
CREATE INDEX IF NOT EXISTS idx_admin_user_sessions_login_time ON admin_user_sessions(login_time DESC);
CREATE INDEX IF NOT EXISTS idx_admin_user_sessions_status ON admin_user_sessions(status);
CREATE INDEX IF NOT EXISTS idx_admin_user_sessions_ip_address ON admin_user_sessions(ip_address);

-- Statistics indexes
CREATE INDEX IF NOT EXISTS idx_audit_log_statistics_period ON audit_log_statistics(period_start, period_end);
CREATE INDEX IF NOT EXISTS idx_audit_log_statistics_type ON audit_log_statistics(period_type, period_start DESC);

-- GIN indexes for JSONB fields
CREATE INDEX IF NOT EXISTS idx_admin_audit_log_action_details_gin ON admin_audit_log USING GIN(action_details);
CREATE INDEX IF NOT EXISTS idx_admin_audit_log_old_values_gin ON admin_audit_log USING GIN(old_values);
CREATE INDEX IF NOT EXISTS idx_admin_audit_log_new_values_gin ON admin_audit_log USING GIN(new_values);
CREATE INDEX IF NOT EXISTS idx_admin_audit_log_tags_gin ON admin_audit_log USING GIN(tags);

-- GIN indexes for array fields
CREATE INDEX IF NOT EXISTS idx_admin_audit_log_compliance_flags_gin ON admin_audit_log USING GIN(compliance_flags);
CREATE INDEX IF NOT EXISTS idx_admin_user_sessions_security_alerts_gin ON admin_user_sessions USING GIN(security_alerts);

-- Functions and Triggers

-- Function to update last_activity in user sessions
CREATE OR REPLACE FUNCTION update_session_activity()
RETURNS TRIGGER AS $$
BEGIN
    -- Update last activity for the session
    UPDATE admin_user_sessions 
    SET 
        last_activity = CURRENT_TIMESTAMP,
        updated_at = CURRENT_TIMESTAMP
    WHERE session_id = NEW.session_id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update session activity on audit log entries
DROP TRIGGER IF EXISTS trigger_update_session_activity ON admin_audit_log;
CREATE TRIGGER trigger_update_session_activity
    AFTER INSERT ON admin_audit_log
    FOR EACH ROW
    WHEN (NEW.session_id IS NOT NULL)
    EXECUTE FUNCTION update_session_activity();

-- Function to clean up old audit logs based on retention period
CREATE OR REPLACE FUNCTION cleanup_expired_audit_logs()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM admin_audit_log 
    WHERE performed_at < (CURRENT_TIMESTAMP - retention_period);
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Function to detect suspicious activity patterns
CREATE OR REPLACE FUNCTION detect_suspicious_activity()
RETURNS TRIGGER AS $$
DECLARE
    recent_failures INTEGER;
    rapid_requests INTEGER;
BEGIN
    -- Check for multiple failed actions from same user/IP in short time
    SELECT COUNT(*) INTO recent_failures
    FROM admin_audit_log 
    WHERE performed_by = NEW.performed_by 
        AND ip_address = NEW.ip_address
        AND status = 'failed'
        AND performed_at > (CURRENT_TIMESTAMP - INTERVAL '15 minutes');
    
    -- Check for rapid requests (potential automation/attack)
    SELECT COUNT(*) INTO rapid_requests
    FROM admin_audit_log 
    WHERE ip_address = NEW.ip_address
        AND performed_at > (CURRENT_TIMESTAMP - INTERVAL '1 minute');
    
    -- Mark session as suspicious if thresholds exceeded
    IF recent_failures >= 5 OR rapid_requests >= 50 THEN
        UPDATE admin_user_sessions 
        SET 
            is_suspicious = TRUE,
            security_alerts = array_append(
                COALESCE(security_alerts, ARRAY[]::TEXT[]), 
                format('Suspicious activity detected at %s', CURRENT_TIMESTAMP)
            ),
            updated_at = CURRENT_TIMESTAMP
        WHERE session_id = NEW.session_id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger for suspicious activity detection
DROP TRIGGER IF EXISTS trigger_detect_suspicious_activity ON admin_audit_log;
CREATE TRIGGER trigger_detect_suspicious_activity
    AFTER INSERT ON admin_audit_log
    FOR EACH ROW
    EXECUTE FUNCTION detect_suspicious_activity();

-- Add comments for documentation
COMMENT ON TABLE admin_audit_log IS 'Comprehensive audit trail for all admin operations with security monitoring and compliance features';
COMMENT ON TABLE admin_user_sessions IS 'Tracks admin user sessions for security monitoring and session management';
COMMENT ON TABLE audit_log_statistics IS 'Aggregated audit statistics for performance and reporting';

-- Initial data setup will be handled by the application layer

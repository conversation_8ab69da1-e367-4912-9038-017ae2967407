/**
 * Audit Trail Search API Endpoint
 * Advanced search capabilities for audit logs
 */

import { NextRequest, NextResponse } from 'next/server';
import { validateApiKey } from '@/lib/auth';
import { adminAuditLogger } from '@/lib/audit/admin-audit-logger';
import { AuditResourceType, AuditAction } from '@/lib/types/audit';
import { log } from '@/lib/logging/logger';

/**
 * GET /api/admin/audit/search
 * Search audit logs with advanced filtering
 */
export async function GET(request: NextRequest) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const searchTerm = searchParams.get('q');

    if (!searchTerm) {
      return NextResponse.json(
        { success: false, error: 'Search term (q) is required' },
        { status: 400 }
      );
    }

    // Parse search options
    const searchOptions = {
      resourceType: searchParams.get('resourceType') as AuditResourceType | undefined,
      action: searchParams.get('action') as AuditAction | undefined,
      performedBy: searchParams.get('performedBy') || undefined,
      limit: Math.min(parseInt(searchParams.get('limit') || '100'), 200) // Max 200 results
    };

    const searchResults = await adminAuditLogger.searchAuditLogs(searchTerm, searchOptions);

    log.admin('audit_search_performed', `Search performed for: "${searchTerm}"`, {
      searchTerm,
      resultsCount: searchResults.length,
      resourceType: searchOptions.resourceType,
      action: searchOptions.action
    });

    return NextResponse.json({
      success: true,
      data: {
        searchTerm,
        results: searchResults,
        count: searchResults.length,
        searchOptions
      }
    });

  } catch (error) {
    log.error('Error searching audit logs', error as Error, {
      component: 'audit-search-api',
      operation: 'search_audit_logs'
    });
    
    return NextResponse.json(
      { success: false, error: 'Failed to search audit logs' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/audit/search
 * Advanced search with complex filters
 */
export async function POST(request: NextRequest) {
  try {
    // Validate admin API key
    const isValid = await validateApiKey(request);
    if (!isValid) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const searchRequest = await request.json();

    if (!searchRequest.searchTerm) {
      return NextResponse.json(
        { success: false, error: 'searchTerm is required' },
        { status: 400 }
      );
    }

    // Validate search options
    const searchOptions = {
      resourceType: searchRequest.resourceType,
      action: searchRequest.action,
      performedBy: searchRequest.performedBy,
      limit: Math.min(searchRequest.limit || 100, 200)
    };

    const searchResults = await adminAuditLogger.searchAuditLogs(
      searchRequest.searchTerm, 
      searchOptions
    );

    // Additional filtering based on advanced criteria
    let filteredResults = searchResults;

    // Filter by date range if provided
    if (searchRequest.dateFrom || searchRequest.dateTo) {
      filteredResults = filteredResults.filter(log => {
        const logDate = log.performedAt;
        
        if (searchRequest.dateFrom && logDate < new Date(searchRequest.dateFrom)) {
          return false;
        }
        
        if (searchRequest.dateTo && logDate > new Date(searchRequest.dateTo)) {
          return false;
        }
        
        return true;
      });
    }

    // Filter by severity if provided
    if (searchRequest.severity) {
      filteredResults = filteredResults.filter(log => 
        log.severity === searchRequest.severity
      );
    }

    // Filter by status if provided
    if (searchRequest.status) {
      filteredResults = filteredResults.filter(log => 
        log.status === searchRequest.status
      );
    }

    // Filter by tags if provided
    if (searchRequest.tags && searchRequest.tags.length > 0) {
      filteredResults = filteredResults.filter(log => 
        log.tags && log.tags.some(tag => searchRequest.tags.includes(tag))
      );
    }

    // Sort results
    if (searchRequest.sortBy) {
      filteredResults.sort((a, b) => {
        const aValue = a[searchRequest.sortBy as keyof typeof a];
        const bValue = b[searchRequest.sortBy as keyof typeof b];
        
        if (searchRequest.sortOrder === 'asc') {
          return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
        } else {
          return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
        }
      });
    }

    // Apply pagination
    const page = searchRequest.page || 1;
    const limit = Math.min(searchRequest.limit || 50, 100);
    const offset = (page - 1) * limit;
    const paginatedResults = filteredResults.slice(offset, offset + limit);

    log.admin('advanced_audit_search_performed', `Advanced search performed`, {
      searchTerm: searchRequest.searchTerm,
      totalResults: searchResults.length,
      filteredResults: filteredResults.length,
      paginatedResults: paginatedResults.length,
      page,
      limit
    });

    return NextResponse.json({
      success: true,
      data: {
        searchTerm: searchRequest.searchTerm,
        results: paginatedResults,
        pagination: {
          page,
          limit,
          total: filteredResults.length,
          totalPages: Math.ceil(filteredResults.length / limit)
        },
        searchOptions: searchOptions,
        appliedFilters: {
          dateFrom: searchRequest.dateFrom,
          dateTo: searchRequest.dateTo,
          severity: searchRequest.severity,
          status: searchRequest.status,
          tags: searchRequest.tags
        }
      }
    });

  } catch (error) {
    log.error('Error performing advanced audit search', error as Error, {
      component: 'audit-search-api',
      operation: 'advanced_search_audit_logs'
    });
    
    return NextResponse.json(
      { success: false, error: 'Failed to perform advanced search' },
      { status: 500 }
    );
  }
}

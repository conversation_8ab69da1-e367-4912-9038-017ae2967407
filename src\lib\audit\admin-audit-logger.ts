/**
 * Admin Audit Logger
 * Comprehensive audit logging system for all admin operations
 */

import { supabaseAdmin } from '@/lib/supabase';
import { log } from '@/lib/logging/logger';
import {
  AuditLogRequest,
  AuditLogQueryOptions,
  AuditLogResponse,
  AdminAuditLog,
  DbAdminAuditLog,
  AuditContext,
  AuditAction,
  AuditResourceType,
  AuditStatus,
  AuditSeverity,
  AuditCategory
} from '@/lib/types/audit';

export class AdminAuditLogger {
  
  /**
   * Log an admin action
   */
  async logAction(request: AuditLogRequest): Promise<string> {
    if (!supabaseAdmin) {
      throw new Error('Admin operations require SUPABASE_SERVICE_ROLE_KEY');
    }

    try {
      const auditData: Omit<DbAdminAuditLog, 'id' | 'performed_at'> = {
        action: request.action,
        resource_type: request.resourceType,
        resource_id: request.resourceId,
        resource_name: request.resourceName,
        performed_by: request.performedBy,
        user_role: request.userRole,
        session_id: request.sessionId,
        request_id: request.requestId,
        http_method: request.httpMethod,
        endpoint: request.endpoint,
        ip_address: request.ipAddress,
        user_agent: request.userAgent,
        action_details: request.actionDetails || null,
        old_values: request.oldValues || null,
        new_values: request.newValues || null,
        status: request.status || 'success',
        error_message: request.errorMessage,
        severity: request.severity || 'medium',
        category: request.category || 'admin',
        tags: request.tags || null,
        retention_period: '7 years', // Default retention period
        is_sensitive: request.isSensitive || false,
        compliance_flags: request.complianceFlags || null
      };

      const { data, error } = await supabaseAdmin
        .from('admin_audit_log')
        .insert([auditData])
        .select('id')
        .single();

      if (error) {
        log.error('Failed to log admin audit action', error, {
          component: 'admin-audit-logger',
          operation: 'log_action',
          action: request.action,
          resourceType: request.resourceType
        });
        throw new Error(`Audit logging failed: ${error.message}`);
      }

      // Log to application logger as well
      log.admin('audit_logged', `Action ${request.action} logged for ${request.resourceType}`, {
        auditId: data.id,
        action: request.action,
        resourceType: request.resourceType,
        performedBy: request.performedBy
      });

      return data.id;
    } catch (error) {
      log.error('Audit logging error', error as Error, {
        component: 'admin-audit-logger',
        operation: 'log_action'
      });
      throw error;
    }
  }

  /**
   * Get audit logs with filtering and pagination
   */
  async getAuditLogs(options: AuditLogQueryOptions = {}): Promise<AuditLogResponse> {
    if (!supabaseAdmin) {
      throw new Error('Admin operations require SUPABASE_SERVICE_ROLE_KEY');
    }

    const page = options.page || 1;
    const limit = options.limit || 50;
    const offset = (page - 1) * limit;

    let query = supabaseAdmin
      .from('admin_audit_log')
      .select('*', { count: 'exact' })
      .order(options.sortBy || 'performed_at', { 
        ascending: options.sortOrder === 'asc' ? true : false 
      });

    // Apply filters
    if (options.action) {
      query = query.eq('action', options.action);
    }
    if (options.resourceType) {
      query = query.eq('resource_type', options.resourceType);
    }
    if (options.resourceId) {
      query = query.eq('resource_id', options.resourceId);
    }
    if (options.performedBy) {
      query = query.eq('performed_by', options.performedBy);
    }
    if (options.status) {
      query = query.eq('status', options.status);
    }
    if (options.severity) {
      query = query.eq('severity', options.severity);
    }
    if (options.category) {
      query = query.eq('category', options.category);
    }
    if (options.dateFrom) {
      query = query.gte('performed_at', options.dateFrom);
    }
    if (options.dateTo) {
      query = query.lte('performed_at', options.dateTo);
    }
    if (options.tags && options.tags.length > 0) {
      query = query.overlaps('tags', options.tags);
    }
    if (options.searchTerm) {
      query = query.or(`resource_name.ilike.%${options.searchTerm}%,action_details::text.ilike.%${options.searchTerm}%,error_message.ilike.%${options.searchTerm}%`);
    }

    // Apply pagination
    query = query.range(offset, offset + limit - 1);

    const { data: logs, error, count } = await query;

    if (error) {
      throw new Error(`Failed to fetch audit logs: ${error.message}`);
    }

    const transformedLogs = logs?.map(log => this.transformDbAuditLogToAuditLog(log)) || [];

    return {
      logs: transformedLogs,
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      }
    };
  }

  /**
   * Get audit statistics for dashboard
   */
  async getAuditStatistics(timeRange: string = '24h'): Promise<{
    totalActions: number;
    successfulActions: number;
    failedActions: number;
    suspiciousActivities: number;
    actionBreakdown: Record<string, number>;
    resourceBreakdown: Record<string, number>;
    topUsers: Array<{
      userId: string;
      actionCount: number;
      lastAction: Date;
    }>;
    recentActivity: AdminAuditLog[];
    errorRate: number;
  }> {
    if (!supabaseAdmin) {
      throw new Error('Admin operations require SUPABASE_SERVICE_ROLE_KEY');
    }

    // Calculate time range
    const now = new Date();
    let startTime: Date;
    
    switch (timeRange) {
      case '1h':
        startTime = new Date(now.getTime() - 60 * 60 * 1000);
        break;
      case '24h':
        startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        break;
      case '7d':
        startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        startTime = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      default:
        startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    }

    const { data: logs, error } = await supabaseAdmin
      .from('admin_audit_log')
      .select('*')
      .gte('performed_at', startTime.toISOString())
      .order('performed_at', { ascending: false });

    if (error) {
      throw new Error(`Failed to fetch audit statistics: ${error.message}`);
    }

    const totalActions = logs?.length || 0;
    const successfulActions = logs?.filter(log => log.status === 'success').length || 0;
    const failedActions = logs?.filter(log => log.status === 'failed').length || 0;
    const suspiciousActivities = logs?.filter(log => log.severity === 'critical' || log.category === 'security').length || 0;

    // Calculate action breakdown
    const actionBreakdown: Record<string, number> = {};
    logs?.forEach(log => {
      actionBreakdown[log.action] = (actionBreakdown[log.action] || 0) + 1;
    });

    // Calculate resource breakdown
    const resourceBreakdown: Record<string, number> = {};
    logs?.forEach(log => {
      resourceBreakdown[log.resource_type] = (resourceBreakdown[log.resource_type] || 0) + 1;
    });

    // Calculate top users
    const userCounts: Record<string, { count: number; lastAction: string }> = {};
    logs?.forEach(log => {
      if (!userCounts[log.performed_by]) {
        userCounts[log.performed_by] = { count: 0, lastAction: log.performed_at };
      }
      userCounts[log.performed_by].count++;
      if (new Date(log.performed_at) > new Date(userCounts[log.performed_by].lastAction)) {
        userCounts[log.performed_by].lastAction = log.performed_at;
      }
    });

    const topUsers = Object.entries(userCounts)
      .map(([userId, data]) => ({
        userId,
        actionCount: data.count,
        lastAction: new Date(data.lastAction)
      }))
      .sort((a, b) => b.actionCount - a.actionCount)
      .slice(0, 10);

    // Get recent activity
    const recentActivity = logs?.slice(0, 10).map(log => this.transformDbAuditLogToAuditLog(log)) || [];

    // Calculate error rate
    const errorRate = totalActions > 0 ? (failedActions / totalActions) * 100 : 0;

    return {
      totalActions,
      successfulActions,
      failedActions,
      suspiciousActivities,
      actionBreakdown,
      resourceBreakdown,
      topUsers,
      recentActivity,
      errorRate
    };
  }

  /**
   * Search audit logs
   */
  async searchAuditLogs(
    searchTerm: string,
    options: {
      resourceType?: AuditResourceType;
      action?: AuditAction;
      performedBy?: string;
      limit?: number;
    } = {}
  ): Promise<AdminAuditLog[]> {
    if (!supabaseAdmin) {
      throw new Error('Admin operations require SUPABASE_SERVICE_ROLE_KEY');
    }

    let query = supabaseAdmin
      .from('admin_audit_log')
      .select('*')
      .or(`resource_name.ilike.%${searchTerm}%,action_details::text.ilike.%${searchTerm}%,error_message.ilike.%${searchTerm}%`)
      .order('performed_at', { ascending: false })
      .limit(options.limit || 100);

    // Apply filters
    if (options.resourceType) {
      query = query.eq('resource_type', options.resourceType);
    }
    if (options.action) {
      query = query.eq('action', options.action);
    }
    if (options.performedBy) {
      query = query.eq('performed_by', options.performedBy);
    }

    const { data: logs, error } = await query;

    if (error) {
      throw new Error(`Failed to search audit logs: ${error.message}`);
    }

    return logs?.map(log => this.transformDbAuditLogToAuditLog(log)) || [];
  }

  /**
   * Transform database audit log to application format
   */
  private transformDbAuditLogToAuditLog(dbLog: DbAdminAuditLog): AdminAuditLog {
    return {
      id: dbLog.id,
      action: dbLog.action,
      resourceType: dbLog.resource_type,
      resourceId: dbLog.resource_id,
      resourceName: dbLog.resource_name,
      performedBy: dbLog.performed_by,
      userRole: dbLog.user_role,
      sessionId: dbLog.session_id,
      requestId: dbLog.request_id,
      httpMethod: dbLog.http_method,
      endpoint: dbLog.endpoint,
      ipAddress: dbLog.ip_address,
      userAgent: dbLog.user_agent,
      actionDetails: dbLog.action_details,
      oldValues: dbLog.old_values,
      newValues: dbLog.new_values,
      status: dbLog.status,
      errorMessage: dbLog.error_message,
      performedAt: new Date(dbLog.performed_at),
      severity: dbLog.severity,
      category: dbLog.category,
      tags: dbLog.tags,
      retentionPeriod: dbLog.retention_period,
      isSensitive: dbLog.is_sensitive,
      complianceFlags: dbLog.compliance_flags
    };
  }

  /**
   * Convenience method to log successful actions
   */
  async logSuccess(
    action: AuditAction,
    resourceType: AuditResourceType,
    context: AuditContext,
    details?: {
      resourceId?: string;
      resourceName?: string;
      actionDetails?: Record<string, any>;
      oldValues?: Record<string, any>;
      newValues?: Record<string, any>;
    }
  ): Promise<string> {
    return this.logAction({
      action,
      resourceType,
      performedBy: context.userId,
      userRole: context.userRole,
      sessionId: context.sessionId,
      requestId: context.requestId,
      ipAddress: context.ipAddress,
      userAgent: context.userAgent,
      status: 'success',
      ...details
    });
  }

  /**
   * Convenience method to log failed actions
   */
  async logFailure(
    action: AuditAction,
    resourceType: AuditResourceType,
    context: AuditContext,
    error: string,
    details?: {
      resourceId?: string;
      resourceName?: string;
      actionDetails?: Record<string, any>;
      severity?: AuditSeverity;
    }
  ): Promise<string> {
    return this.logAction({
      action,
      resourceType,
      performedBy: context.userId,
      userRole: context.userRole,
      sessionId: context.sessionId,
      requestId: context.requestId,
      ipAddress: context.ipAddress,
      userAgent: context.userAgent,
      status: 'failed',
      errorMessage: error,
      severity: details?.severity || 'high',
      ...details
    });
  }

  /**
   * Clean up expired audit logs
   */
  async cleanupExpiredLogs(): Promise<number> {
    if (!supabaseAdmin) {
      throw new Error('Admin operations require SUPABASE_SERVICE_ROLE_KEY');
    }

    try {
      const { data, error } = await supabaseAdmin
        .rpc('cleanup_expired_audit_logs');

      if (error) {
        throw new Error(`Failed to cleanup expired logs: ${error.message}`);
      }

      log.admin('audit_cleanup', `Cleaned up ${data} expired audit logs`);
      return data || 0;
    } catch (error) {
      log.error('Audit cleanup error', error as Error, {
        component: 'admin-audit-logger',
        operation: 'cleanup_expired_logs'
      });
      throw error;
    }
  }
}

// Singleton instance
export const adminAuditLogger = new AdminAuditLogger();

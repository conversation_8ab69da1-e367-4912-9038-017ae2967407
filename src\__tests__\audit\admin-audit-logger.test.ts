/**
 * Admin Audit Logger Tests
 * Comprehensive test suite for audit trail functionality
 */

import { AdminAuditLogger } from '@/lib/audit/admin-audit-logger';
import { supabaseAdmin } from '@/lib/supabase';
import { AuditLogRequest, AuditContext } from '@/lib/types/audit';

// Mock Supabase
jest.mock('@/lib/supabase', () => ({
  supabaseAdmin: {
    from: jest.fn(() => ({
      insert: jest.fn(() => ({
        select: jest.fn(() => ({
          single: jest.fn()
        }))
      })),
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          order: jest.fn(() => ({
            range: jest.fn()
          }))
        })),
        gte: jest.fn(),
        lte: jest.fn(),
        overlaps: jest.fn(),
        or: jest.fn()
      })),
      rpc: jest.fn()
    }))
  }
}));

// Mock logger
jest.mock('@/lib/logging/logger', () => ({
  log: {
    admin: jest.fn(),
    error: jest.fn()
  }
}));

describe('AdminAuditLogger', () => {
  let auditLogger: AdminAuditLogger;
  let mockSupabase: any;

  beforeEach(() => {
    auditLogger = new AdminAuditLogger();
    mockSupabase = supabaseAdmin;
    jest.clearAllMocks();
  });

  describe('logAction', () => {
    it('should successfully log an audit action', async () => {
      const mockAuditId = 'test-audit-id';
      const mockInsertResponse = {
        data: { id: mockAuditId },
        error: null
      };

      mockSupabase.from().insert().select().single.mockResolvedValue(mockInsertResponse);

      const auditRequest: AuditLogRequest = {
        action: 'create_tool',
        resourceType: 'tool',
        resourceId: 'tool-123',
        resourceName: 'Test Tool',
        performedBy: 'admin-user',
        userRole: 'admin',
        sessionId: 'session-123',
        requestId: 'request-123',
        httpMethod: 'POST',
        endpoint: '/api/admin/tools',
        ipAddress: '***********',
        userAgent: 'Mozilla/5.0',
        actionDetails: { test: true },
        status: 'success',
        severity: 'medium',
        category: 'admin'
      };

      const result = await auditLogger.logAction(auditRequest);

      expect(result).toBe(mockAuditId);
      expect(mockSupabase.from).toHaveBeenCalledWith('admin_audit_log');
      expect(mockSupabase.from().insert).toHaveBeenCalledWith([
        expect.objectContaining({
          action: 'create_tool',
          resource_type: 'tool',
          resource_id: 'tool-123',
          resource_name: 'Test Tool',
          performed_by: 'admin-user',
          user_role: 'admin',
          session_id: 'session-123',
          request_id: 'request-123',
          http_method: 'POST',
          endpoint: '/api/admin/tools',
          ip_address: '***********',
          user_agent: 'Mozilla/5.0',
          action_details: { test: true },
          status: 'success',
          severity: 'medium',
          category: 'admin',
          retention_period: '7 years',
          is_sensitive: false
        })
      ]);
    });

    it('should handle audit logging errors gracefully', async () => {
      const mockError = new Error('Database connection failed');
      mockSupabase.from().insert().select().single.mockResolvedValue({
        data: null,
        error: mockError
      });

      const auditRequest: AuditLogRequest = {
        action: 'create_tool',
        resourceType: 'tool',
        performedBy: 'admin-user'
      };

      await expect(auditLogger.logAction(auditRequest)).rejects.toThrow(
        'Audit logging failed: Database connection failed'
      );
    });

    it('should throw error when supabaseAdmin is not available', async () => {
      // Temporarily mock supabaseAdmin as null
      (supabaseAdmin as any) = null;

      const auditRequest: AuditLogRequest = {
        action: 'create_tool',
        resourceType: 'tool',
        performedBy: 'admin-user'
      };

      await expect(auditLogger.logAction(auditRequest)).rejects.toThrow(
        'Admin operations require SUPABASE_SERVICE_ROLE_KEY'
      );
    });
  });

  describe('getAuditLogs', () => {
    it('should retrieve audit logs with pagination', async () => {
      const mockLogs = [
        {
          id: 'log-1',
          action: 'create_tool',
          resource_type: 'tool',
          resource_id: 'tool-1',
          resource_name: 'Tool 1',
          performed_by: 'admin-1',
          performed_at: '2024-01-01T00:00:00Z',
          status: 'success',
          severity: 'medium',
          category: 'admin',
          is_sensitive: false
        }
      ];

      const mockResponse = {
        data: mockLogs,
        error: null,
        count: 1
      };

      // Mock the query chain
      const mockQuery = {
        eq: jest.fn().mockReturnThis(),
        gte: jest.fn().mockReturnThis(),
        lte: jest.fn().mockReturnThis(),
        overlaps: jest.fn().mockReturnThis(),
        or: jest.fn().mockReturnThis(),
        range: jest.fn().mockResolvedValue(mockResponse)
      };

      const mockSelect = {
        order: jest.fn().mockReturnValue(mockQuery)
      };

      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue(mockSelect)
      });

      const options = {
        page: 1,
        limit: 10,
        action: 'create_tool' as const,
        resourceType: 'tool' as const
      };

      const result = await auditLogger.getAuditLogs(options);

      expect(result.logs).toHaveLength(1);
      expect(result.pagination.total).toBe(1);
      expect(result.pagination.page).toBe(1);
      expect(result.pagination.limit).toBe(10);
      expect(result.logs[0].action).toBe('create_tool');
    });

    it('should apply filters correctly', async () => {
      const mockResponse = {
        data: [],
        error: null,
        count: 0
      };

      const mockQuery = {
        eq: jest.fn().mockReturnThis(),
        gte: jest.fn().mockReturnThis(),
        lte: jest.fn().mockReturnThis(),
        overlaps: jest.fn().mockReturnThis(),
        or: jest.fn().mockReturnThis(),
        range: jest.fn().mockResolvedValue(mockResponse)
      };

      const mockSelect = {
        order: jest.fn().mockReturnValue(mockQuery)
      };

      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue(mockSelect)
      });

      const options = {
        action: 'update_tool' as const,
        resourceType: 'tool' as const,
        performedBy: 'admin-user',
        status: 'success' as const,
        severity: 'high' as const,
        dateFrom: '2024-01-01',
        dateTo: '2024-01-31',
        tags: ['important', 'security']
      };

      await auditLogger.getAuditLogs(options);

      expect(mockQuery.eq).toHaveBeenCalledWith('action', 'update_tool');
      expect(mockQuery.eq).toHaveBeenCalledWith('resource_type', 'tool');
      expect(mockQuery.eq).toHaveBeenCalledWith('performed_by', 'admin-user');
      expect(mockQuery.eq).toHaveBeenCalledWith('status', 'success');
      expect(mockQuery.eq).toHaveBeenCalledWith('severity', 'high');
      expect(mockQuery.gte).toHaveBeenCalledWith('performed_at', '2024-01-01');
      expect(mockQuery.lte).toHaveBeenCalledWith('performed_at', '2024-01-31');
      expect(mockQuery.overlaps).toHaveBeenCalledWith('tags', ['important', 'security']);
    });
  });

  describe('getAuditStatistics', () => {
    it('should calculate audit statistics correctly', async () => {
      const mockLogs = [
        {
          action: 'create_tool',
          resource_type: 'tool',
          performed_by: 'admin-1',
          performed_at: '2024-01-01T12:00:00Z',
          status: 'success',
          severity: 'medium',
          category: 'admin'
        },
        {
          action: 'update_tool',
          resource_type: 'tool',
          performed_by: 'admin-1',
          performed_at: '2024-01-01T13:00:00Z',
          status: 'failed',
          severity: 'high',
          category: 'admin'
        },
        {
          action: 'delete_tool',
          resource_type: 'tool',
          performed_by: 'admin-2',
          performed_at: '2024-01-01T14:00:00Z',
          status: 'success',
          severity: 'critical',
          category: 'security'
        }
      ];

      const mockResponse = {
        data: mockLogs,
        error: null
      };

      const mockSelect = {
        gte: jest.fn().mockReturnThis(),
        order: jest.fn().mockResolvedValue(mockResponse)
      };

      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnValue(mockSelect)
      });

      const result = await auditLogger.getAuditStatistics('24h');

      expect(result.totalActions).toBe(3);
      expect(result.successfulActions).toBe(2);
      expect(result.failedActions).toBe(1);
      expect(result.suspiciousActivities).toBe(1); // critical severity
      expect(result.actionBreakdown).toEqual({
        'create_tool': 1,
        'update_tool': 1,
        'delete_tool': 1
      });
      expect(result.resourceBreakdown).toEqual({
        'tool': 3
      });
      expect(result.topUsers).toHaveLength(2);
      expect(result.errorRate).toBe(33.33);
    });
  });

  describe('logSuccess', () => {
    it('should log successful action with context', async () => {
      const mockAuditId = 'success-audit-id';
      mockSupabase.from().insert().select().single.mockResolvedValue({
        data: { id: mockAuditId },
        error: null
      });

      const context: AuditContext = {
        userId: 'admin-user',
        userRole: 'admin',
        sessionId: 'session-123',
        requestId: 'request-123',
        ipAddress: '***********',
        userAgent: 'Mozilla/5.0'
      };

      const result = await auditLogger.logSuccess(
        'create_tool',
        'tool',
        context,
        {
          resourceId: 'tool-123',
          resourceName: 'Test Tool',
          actionDetails: { created: true }
        }
      );

      expect(result).toBe(mockAuditId);
      expect(mockSupabase.from().insert).toHaveBeenCalledWith([
        expect.objectContaining({
          action: 'create_tool',
          resource_type: 'tool',
          resource_id: 'tool-123',
          resource_name: 'Test Tool',
          performed_by: 'admin-user',
          status: 'success',
          action_details: { created: true }
        })
      ]);
    });
  });

  describe('logFailure', () => {
    it('should log failed action with error details', async () => {
      const mockAuditId = 'failure-audit-id';
      mockSupabase.from().insert().select().single.mockResolvedValue({
        data: { id: mockAuditId },
        error: null
      });

      const context: AuditContext = {
        userId: 'admin-user',
        userRole: 'admin',
        sessionId: 'session-123',
        requestId: 'request-123',
        ipAddress: '***********',
        userAgent: 'Mozilla/5.0'
      };

      const result = await auditLogger.logFailure(
        'delete_tool',
        'tool',
        context,
        'Tool not found',
        {
          resourceId: 'tool-404',
          severity: 'high'
        }
      );

      expect(result).toBe(mockAuditId);
      expect(mockSupabase.from().insert).toHaveBeenCalledWith([
        expect.objectContaining({
          action: 'delete_tool',
          resource_type: 'tool',
          resource_id: 'tool-404',
          performed_by: 'admin-user',
          status: 'failed',
          error_message: 'Tool not found',
          severity: 'high'
        })
      ]);
    });
  });

  describe('searchAuditLogs', () => {
    it('should search audit logs with term and filters', async () => {
      const mockLogs = [
        {
          id: 'search-result-1',
          action: 'update_tool',
          resource_type: 'tool',
          resource_name: 'Search Test Tool',
          performed_by: 'admin-user',
          performed_at: '2024-01-01T00:00:00Z',
          status: 'success',
          severity: 'medium',
          category: 'admin',
          is_sensitive: false
        }
      ];

      const mockResponse = {
        data: mockLogs,
        error: null
      };

      const mockQuery = {
        eq: jest.fn().mockReturnThis(),
        order: jest.fn().mockReturnThis(),
        limit: jest.fn().mockResolvedValue(mockResponse)
      };

      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        or: jest.fn().mockReturnValue(mockQuery)
      });

      const result = await auditLogger.searchAuditLogs('Search Test', {
        resourceType: 'tool',
        action: 'update_tool',
        limit: 50
      });

      expect(result).toHaveLength(1);
      expect(result[0].resourceName).toBe('Search Test Tool');
      expect(mockQuery.eq).toHaveBeenCalledWith('resource_type', 'tool');
      expect(mockQuery.eq).toHaveBeenCalledWith('action', 'update_tool');
    });
  });

  describe('cleanupExpiredLogs', () => {
    it('should clean up expired audit logs', async () => {
      const mockCleanupCount = 42;
      mockSupabase.rpc.mockResolvedValue({
        data: mockCleanupCount,
        error: null
      });

      const result = await auditLogger.cleanupExpiredLogs();

      expect(result).toBe(mockCleanupCount);
      expect(mockSupabase.rpc).toHaveBeenCalledWith('cleanup_expired_audit_logs');
    });

    it('should handle cleanup errors', async () => {
      const mockError = new Error('Cleanup failed');
      mockSupabase.rpc.mockResolvedValue({
        data: null,
        error: mockError
      });

      await expect(auditLogger.cleanupExpiredLogs()).rejects.toThrow(
        'Failed to cleanup expired logs: Cleanup failed'
      );
    });
  });
});

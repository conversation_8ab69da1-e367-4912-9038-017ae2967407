/**
 * Audit Trail API Tests
 * Test suite for audit trail API endpoints
 */

import { NextRequest } from 'next/server';
import { GET, POST, DELETE } from '@/app/api/admin/audit/route';
import { validateApiKey } from '@/lib/auth';
import { adminAuditLogger } from '@/lib/audit/admin-audit-logger';

// Mock dependencies
jest.mock('@/lib/auth', () => ({
  validateApiKey: jest.fn()
}));

jest.mock('@/lib/audit/admin-audit-logger', () => ({
  adminAuditLogger: {
    getAuditLogs: jest.fn(),
    logAction: jest.fn(),
    cleanupExpiredLogs: jest.fn()
  }
}));

jest.mock('@/lib/logging/logger', () => ({
  log: {
    admin: jest.fn(),
    error: jest.fn()
  }
}));

describe('/api/admin/audit', () => {
  let mockValidateApiKey: jest.MockedFunction<typeof validateApiKey>;
  let mockAdminAuditLogger: jest.Mocked<typeof adminAuditLogger>;

  beforeEach(() => {
    mockValidateApiKey = validateApiKey as jest.MockedFunction<typeof validateApiKey>;
    mockAdminAuditLogger = adminAuditLogger as jest.Mocked<typeof adminAuditLogger>;
    jest.clearAllMocks();
  });

  describe('GET /api/admin/audit', () => {
    it('should return audit logs with pagination', async () => {
      mockValidateApiKey.mockResolvedValue(true);

      const mockAuditLogs = {
        logs: [
          {
            id: 'audit-1',
            action: 'create_tool',
            resourceType: 'tool',
            resourceId: 'tool-123',
            resourceName: 'Test Tool',
            performedBy: 'admin-user',
            performedAt: new Date('2024-01-01T00:00:00Z'),
            status: 'success',
            severity: 'medium',
            category: 'admin',
            isSensitive: false
          }
        ],
        pagination: {
          page: 1,
          limit: 50,
          total: 1,
          totalPages: 1
        }
      };

      mockAdminAuditLogger.getAuditLogs.mockResolvedValue(mockAuditLogs);

      const request = new NextRequest('http://localhost:3000/api/admin/audit?page=1&limit=50&action=create_tool');

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.logs).toHaveLength(1);
      expect(data.data.pagination.total).toBe(1);
      expect(mockAdminAuditLogger.getAuditLogs).toHaveBeenCalledWith({
        page: 1,
        limit: 50,
        action: 'create_tool',
        resourceType: undefined,
        resourceId: undefined,
        performedBy: undefined,
        status: undefined,
        severity: undefined,
        category: undefined,
        dateFrom: undefined,
        dateTo: undefined,
        searchTerm: undefined,
        tags: undefined,
        includeDetails: false,
        sortBy: 'performed_at',
        sortOrder: 'desc'
      });
    });

    it('should apply query filters correctly', async () => {
      mockValidateApiKey.mockResolvedValue(true);
      mockAdminAuditLogger.getAuditLogs.mockResolvedValue({
        logs: [],
        pagination: { page: 1, limit: 10, total: 0, totalPages: 0 }
      });

      const queryParams = new URLSearchParams({
        page: '2',
        limit: '25',
        action: 'update_tool',
        resourceType: 'tool',
        resourceId: 'tool-456',
        performedBy: 'admin-user',
        status: 'success',
        severity: 'high',
        category: 'admin',
        dateFrom: '2024-01-01',
        dateTo: '2024-01-31',
        searchTerm: 'test search',
        tags: 'important,security',
        includeDetails: 'true',
        sortBy: 'action',
        sortOrder: 'asc'
      });

      const request = new NextRequest(`http://localhost:3000/api/admin/audit?${queryParams}`);

      await GET(request);

      expect(mockAdminAuditLogger.getAuditLogs).toHaveBeenCalledWith({
        page: 2,
        limit: 25,
        action: 'update_tool',
        resourceType: 'tool',
        resourceId: 'tool-456',
        performedBy: 'admin-user',
        status: 'success',
        severity: 'high',
        category: 'admin',
        dateFrom: '2024-01-01',
        dateTo: '2024-01-31',
        searchTerm: 'test search',
        tags: ['important', 'security'],
        includeDetails: true,
        sortBy: 'action',
        sortOrder: 'asc'
      });
    });

    it('should enforce maximum limit of 100', async () => {
      mockValidateApiKey.mockResolvedValue(true);
      mockAdminAuditLogger.getAuditLogs.mockResolvedValue({
        logs: [],
        pagination: { page: 1, limit: 100, total: 0, totalPages: 0 }
      });

      const request = new NextRequest('http://localhost:3000/api/admin/audit?limit=500');

      await GET(request);

      expect(mockAdminAuditLogger.getAuditLogs).toHaveBeenCalledWith(
        expect.objectContaining({
          limit: 100 // Should be capped at 100
        })
      );
    });

    it('should return 401 for unauthorized requests', async () => {
      mockValidateApiKey.mockResolvedValue(false);

      const request = new NextRequest('http://localhost:3000/api/admin/audit');

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Unauthorized');
      expect(mockAdminAuditLogger.getAuditLogs).not.toHaveBeenCalled();
    });

    it('should handle audit logger errors', async () => {
      mockValidateApiKey.mockResolvedValue(true);
      mockAdminAuditLogger.getAuditLogs.mockRejectedValue(new Error('Database error'));

      const request = new NextRequest('http://localhost:3000/api/admin/audit');

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Failed to fetch audit logs');
    });
  });

  describe('POST /api/admin/audit', () => {
    it('should create manual audit log entry', async () => {
      mockValidateApiKey.mockResolvedValue(true);
      mockAdminAuditLogger.logAction.mockResolvedValue('manual-audit-id');

      const auditRequest = {
        action: 'manual_action',
        resourceType: 'system',
        performedBy: 'admin-user',
        resourceId: 'system-123',
        resourceName: 'Manual Test',
        actionDetails: { manual: true }
      };

      const request = new NextRequest('http://localhost:3000/api/admin/audit', {
        method: 'POST',
        headers: {
          'content-type': 'application/json',
          'x-forwarded-for': '***********',
          'user-agent': 'Mozilla/5.0'
        },
        body: JSON.stringify(auditRequest)
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.auditId).toBe('manual-audit-id');
      expect(mockAdminAuditLogger.logAction).toHaveBeenCalledWith({
        ...auditRequest,
        ipAddress: '***********',
        userAgent: 'Mozilla/5.0',
        httpMethod: 'POST',
        endpoint: '/api/admin/audit'
      });
    });

    it('should validate required fields', async () => {
      mockValidateApiKey.mockResolvedValue(true);

      const incompleteRequest = {
        action: 'test_action'
        // Missing resourceType and performedBy
      };

      const request = new NextRequest('http://localhost:3000/api/admin/audit', {
        method: 'POST',
        headers: { 'content-type': 'application/json' },
        body: JSON.stringify(incompleteRequest)
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Missing required fields: action, resourceType, performedBy');
      expect(mockAdminAuditLogger.logAction).not.toHaveBeenCalled();
    });

    it('should return 401 for unauthorized requests', async () => {
      mockValidateApiKey.mockResolvedValue(false);

      const request = new NextRequest('http://localhost:3000/api/admin/audit', {
        method: 'POST',
        headers: { 'content-type': 'application/json' },
        body: JSON.stringify({ action: 'test', resourceType: 'test', performedBy: 'test' })
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Unauthorized');
    });

    it('should handle audit logging errors', async () => {
      mockValidateApiKey.mockResolvedValue(true);
      mockAdminAuditLogger.logAction.mockRejectedValue(new Error('Audit system error'));

      const auditRequest = {
        action: 'test_action',
        resourceType: 'test',
        performedBy: 'admin-user'
      };

      const request = new NextRequest('http://localhost:3000/api/admin/audit', {
        method: 'POST',
        headers: { 'content-type': 'application/json' },
        body: JSON.stringify(auditRequest)
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Failed to create audit log');
    });
  });

  describe('DELETE /api/admin/audit', () => {
    it('should clean up expired audit logs', async () => {
      mockValidateApiKey.mockResolvedValue(true);
      mockAdminAuditLogger.cleanupExpiredLogs.mockResolvedValue(25);

      const request = new NextRequest('http://localhost:3000/api/admin/audit', {
        method: 'DELETE'
      });

      const response = await DELETE(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.cleanedLogs).toBe(25);
      expect(data.data.message).toBe('Cleaned up 25 expired audit logs');
      expect(mockAdminAuditLogger.cleanupExpiredLogs).toHaveBeenCalled();
    });

    it('should handle singular message for one cleaned log', async () => {
      mockValidateApiKey.mockResolvedValue(true);
      mockAdminAuditLogger.cleanupExpiredLogs.mockResolvedValue(1);

      const request = new NextRequest('http://localhost:3000/api/admin/audit', {
        method: 'DELETE'
      });

      const response = await DELETE(request);
      const data = await response.json();

      expect(data.data.message).toBe('Cleaned up 1 expired audit log');
    });

    it('should return 401 for unauthorized requests', async () => {
      mockValidateApiKey.mockResolvedValue(false);

      const request = new NextRequest('http://localhost:3000/api/admin/audit', {
        method: 'DELETE'
      });

      const response = await DELETE(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Unauthorized');
    });

    it('should handle cleanup errors', async () => {
      mockValidateApiKey.mockResolvedValue(true);
      mockAdminAuditLogger.cleanupExpiredLogs.mockRejectedValue(new Error('Cleanup failed'));

      const request = new NextRequest('http://localhost:3000/api/admin/audit', {
        method: 'DELETE'
      });

      const response = await DELETE(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Failed to cleanup audit logs');
    });
  });
});
